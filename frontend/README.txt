Frontend quick start (Vite + React)
-----------------------------------
1) Create app:
   npm create vite@latest crop-yield-ui -- --template react
   cd crop-yield-ui
   npm install
   npm install axios i18next react-i18next

2) Replace files:
   - Copy the provided src/App.jsx to your project at src/App.jsx
   - Copy src/i18n.js and src/translations/*.json to your project

3) Run:
   npm run dev

4) Ensure backend (Express) on :5000 and ML service (FastAPI) on :8000 are running.
