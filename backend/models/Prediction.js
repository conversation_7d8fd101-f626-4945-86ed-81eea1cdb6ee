// backend/models/Prediction.js
import mongoose from "mongoose";

const PredictionSchema = new mongoose.Schema({
  createdAt: { type: Date, default: Date.now },
  userId: { type: String, default: "demo" },
  input: { type: Object, required: true },
  prediction_t_per_ha: { type: Number, required: true },
  recommendations: { type: [String], default: [] }
});

export default mongoose.model("Prediction", PredictionSchema);
