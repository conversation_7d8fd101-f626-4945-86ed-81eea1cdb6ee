# Crop Yield MVP Starter (MERN + Python)
Generated: 2025-08-29T06:07:12

## Contents
- **data/sample_yield.csv** — synthetic Odisha dataset for rice/wheat/maize.
- **ml/** — training script and FastAPI prediction service.
- **backend/** — Express API that proxies to ML, saves predictions, and pulls weather.
- **frontend/** — React skeleton + i18n (EN/HI/OD).

## Quick Run (Local)
1. Python ML service:
   ```bash
   cd ml
   python3 -m venv .venv && source .venv/bin/activate  # Windows: .venv\Scripts\activate
   pip install -r requirements.txt
   python train.py    # trains and saves models/yield_model.pkl
   uvicorn app:app --reload --port 8000
   ```

2. Node backend:
   ```bash
   cd backend
   cp .env.example .env  # set MONGO_URI and OPENWEATHER_API_KEY
   npm install
   npm run dev  # http://localhost:5000
   ```

3. Frontend (Vite):
   Follow `frontend/README.txt` to scaffold a Vite React app and copy files.
   Add `.env` in React root with: `VITE_API_BASE=http://localhost:5000`

## Notes
- All numbers/thresholds are for DEMO ONLY.
- Improve model by using real datasets and hyperparameter tuning.
