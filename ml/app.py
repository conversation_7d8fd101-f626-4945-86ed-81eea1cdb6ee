# ml/app.py
import os, json, joblib
from fastapi import FastAPI
from pydantic import BaseModel, Field
import pandas as pd
from fastapi.middleware.cors import CORSMiddleware

MODEL_DIR = os.environ.get("MODEL_DIR", os.path.join(os.path.dirname(__file__), "models"))
MODEL_PATH = os.path.join(MODEL_DIR, "yield_model.pkl")

app = FastAPI(title="Crop Yield ML Service", version="0.1.0")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

class YieldInput(BaseModel):
    state: str = "Odisha"
    district: str = "Khurda"
    crop: str = Field("rice", description="rice|wheat|maize")
    season: str = Field("Kharif", description="Kharif|Rabi")
    year: int = 2022
    area_ha: float = 1.0
    rainfall_mm: float = 800.0
    temp_c: float = 28.0
    soil_n: float = 40.0
    soil_p: float = 25.0
    soil_k: float = 120.0
    soil_ph: float = 6.8
    irrigation_mm: float = 200.0
    fertilizer_npk_kg: float = 120.0
    pest_index: float = 0.2

def load_model():
    if not os.path.exists(MODEL_PATH):
        raise RuntimeError("Model not found. Train it first with ml/train.py")
    return joblib.load(MODEL_PATH)

model = load_model()

def recommendations(x: dict, y_pred: float):
    recs = []
    crop = x.get("crop","").lower()
    # simple thresholds
    if x.get("soil_ph", 6.8) < 6.0:
        recs.append("Soil pH low: apply lime (200–400 kg/ha).")
    if x.get("soil_ph", 6.8) > 7.5:
        recs.append("Soil pH high: apply gypsum (100–200 kg/ha).")
    if x.get("soil_n", 0) < 40:
        recs.append("Low Nitrogen: add urea 40–60 kg/ha split doses.")
    if x.get("soil_p", 0) < 25:
        recs.append("Low Phosphorus: add DAP 30–40 kg/ha at sowing.")
    if x.get("soil_k", 0) < 100:
        recs.append("Low Potassium: add MOP 20–40 kg/ha.")
    if x.get("rainfall_mm", 0) < 500 and x.get("irrigation_mm", 0) < 200:
        recs.append("Water stress risk: increase irrigation by 100–150 mm over season.")
    if x.get("pest_index", 0) > 0.6:
        recs.append("High pest risk: use IPM (traps, neem oil) and scout weekly.")
    # crop tips
    if crop == "rice":
        recs.append("Rice tip: maintain 2–5 cm standing water during tillering; avoid over-flooding at booting.")
    elif crop == "wheat":
        recs.append("Wheat tip: ensure timely irrigation at CRI, booting, and grain filling stages.")
    elif crop == "maize":
        recs.append("Maize tip: ensure N top-dressing at V6–V8 stage.")
    # yield-based
    if y_pred < 2.5:
        recs.append("Overall low yield predicted: consider better seed variety and balanced NPK.")
    return recs

@app.post("/predict")
def predict(payload: YieldInput):
    x = payload.dict()
    X = pd.DataFrame([x])
    y = float(model.predict(X)[0])
    return {"prediction_t_per_ha": round(y, 2), "recommendations": recommendations(x, y), "input": x}

@app.get("/health")
def health():
    return {"status":"ok"}
