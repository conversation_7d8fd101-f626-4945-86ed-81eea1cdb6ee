# ml/train.py
import os, json, joblib
import pandas as pd
from sklearn.model_selection import train_test_split
from sklearn.compose import ColumnTransformer
from sklearn.preprocessing import OneHotEncoder
from sklearn.impute import SimpleImputer
from sklearn.pipeline import Pipeline
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import r2_score, mean_absolute_error

DATA_PATH = os.environ.get("DATA_PATH", os.path.join(os.path.dirname(__file__), "..", "data", "sample_yield.csv"))
MODEL_DIR = os.environ.get("MODEL_DIR", os.path.join(os.path.dirname(__file__), "models"))
os.makedirs(MODEL_DIR, exist_ok=True)

def main():
    df = pd.read_csv(DATA_PATH)
    target = "yield_t_per_ha"
    y = df[target]
    X = df.drop(columns=[target])

    num_cols = ["year","area_ha","rainfall_mm","temp_c","soil_n","soil_p","soil_k","soil_ph","irrigation_mm","fertilizer_npk_kg","pest_index"]
    cat_cols = ["state","district","crop","season"]

    num_tf = Pipeline(steps=[
        ("imputer", SimpleImputer(strategy="median"))
    ])
    cat_tf = Pipeline(steps=[
        ("imputer", SimpleImputer(strategy="most_frequent")),
        ("ohe", OneHotEncoder(handle_unknown="ignore"))
    ])
    preprocessor = ColumnTransformer(
        transformers=[
            ("num", num_tf, num_cols),
            ("cat", cat_tf, cat_cols),
        ]
    )

    model = RandomForestRegressor(n_estimators=250, random_state=42)
    pipe = Pipeline(steps=[("prep", preprocessor), ("model", model)])

    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
    pipe.fit(X_train, y_train)

    pred = pipe.predict(X_test)
    r2 = r2_score(y_test, pred)
    mae = mean_absolute_error(y_test, pred)

    model_path = os.path.join(MODEL_DIR, "yield_model.pkl")
    joblib.dump(pipe, model_path)

    meta = {
        "num_features": num_cols,
        "cat_features": cat_cols,
        "target": target,
        "metrics": {"r2": r2, "mae": mae}
    }
    with open(os.path.join(MODEL_DIR, "meta.json"), "w") as f:
        json.dump(meta, f, indent=2)

    print(f"Saved model to {model_path}")
    print(f"R2: {r2:.3f}, MAE: {mae:.3f} t/ha")

if __name__ == "__main__":
    main()
